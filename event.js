const eventList = document.getElementById('event-list');
const events = getEvents();

if (events.length === 0) {
  eventList.innerHTML = "<p>No events added yet.</p>";
} else {
  events.forEach((event, index) => {
    const div = document.createElement('div');
    div.className = 'event-card';
    div.innerHTML = `
      <h3>${event.title}</h3>
      <small>${event.date} at ${event.time}</small>
      <p>${event.description || 'No description'}</p>
      <a class="btn-small" href="details.html?id=${index}">View Details</a>
    `;
    eventList.appendChild(div);
  });
}
