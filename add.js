document.getElementById('event-form').addEventListener('submit', function (e) {
  e.preventDefault();

  const title = document.getElementById('title').value.trim();
  const date = document.getElementById('date').value;
  const time = document.getElementById('time').value;
  const description = document.getElementById('description').value.trim();

  const newEvent = { title, date, time, description };

  const events = getEvents();
  events.push(newEvent);
  saveEvents(events);

  window.location.href = 'event.html'; // Redirect after adding
});
