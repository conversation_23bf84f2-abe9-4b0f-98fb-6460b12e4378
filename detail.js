const params = new URLSearchParams(window.location.search);
const eventId = parseInt(params.get('id'));
const event = getEvents()[eventId];
const detailDiv = document.getElementById('event-detail');

if (!event) {
  detailDiv.innerHTML = "<p>Event not found.</p>";
} else {
  detailDiv.innerHTML = `
    <h2>${event.title}</h2>
    <p><strong>Date:</strong> ${event.date}</p>
    <p><strong>Time:</strong> ${event.time}</p>
    <p><strong>Description:</strong> ${event.description || 'No description'}</p>
  `;
}
